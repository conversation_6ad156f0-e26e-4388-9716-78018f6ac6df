import type { z } from 'zod';

import type { Context, TRPCProcedure } from '../../types';
import { StorageInputSchema } from './types';

type MutationArgs = {
  input: z.infer<typeof StorageInputSchema>;
  ctx: Context;
};

export const updateStorageMutation = async ({ input, ctx }: MutationArgs) => {
  const { id, images, mapImage, ...data } = input;
  const { asyncTx, tenantId } = ctx;
  await asyncTx(async (tx) => {
    await tx.storageMapImage.deleteMany({ where: { storageId: id } });
    await tx.storage.update({
      where: { tenantId, id },
      data: {
        ...data,
        images: { deleteMany: {}, createMany: { data: images } },
        ...(mapImage && {
          mapImage: { create: { key: mapImage.key, description: mapImage.description } },
        }),
      },
    });
  });
  return 'OK';
};

export const updateStorage = (p: TRPCProcedure) =>
  p.input(StorageInputSchema).mutation(updateStorageMutation);
