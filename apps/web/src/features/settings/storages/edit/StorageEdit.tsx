import { Alert, Paper, Stack } from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { MainLayout } from '@/components/core/layout/MainLayout';
import { tenantQueryKey, useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { storageRouters } from '@/router/routes/settings/storages';
import { Close, Edit } from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import { numberOrNull } from 'common';
import type { Storage } from 'lambda-api';
import {
  RhfStorage,
  type StorageState,
  StorageStateSchema,
  storageToState,
  uploadMapImageToS3,
  uploadStorageImageToS3,
} from '../common/RhfStorage';

type Props = {
  storage: Storage;
};

export const StorageEdit = ({ storage }: Props) => {
  const id = storage.id;
  const { map, storages } = useAppContext();
  const { control, watch, formState, handleSubmit } = useForm<StorageState>({
    mode: 'onChange',
    defaultValues: storageToState(storage, storages, map),
    resolver: zodResolver(StorageStateSchema),
  });
  const queryClient = useQueryClient();
  const { error, mutateAsync } = trpc.storages.update.useMutation({
    onSuccess: async () => {
      const queryKeys = [getQueryKey(trpc.storages.get, { id }), tenantQueryKey];
      await Promise.all(queryKeys.map((queryKey) => queryClient.invalidateQueries({ queryKey })));
      navigate({ to: '/settings/storages' });
      enqueueSnackbar('保管所を編集しました', { variant: 'success' });
    },
  });
  const navigate = useNavigate();
  const title = storageRouters.meta.edit.useTitle();
  const submit = async (state: StorageState) => {
    const images = await uploadStorageImageToS3(id, state.images);
    const mapImage = await uploadMapImageToS3(id, state.mapImage);
    await mutateAsync({
      id,
      name: state.name,
      code: state.code,
      sortOrder: state.sortOrder,
      sectionName: state.sectionName,
      managerName: state.managerName,
      address: state.address,
      lat: numberOrNull(state.lat, map.defaultLatitude),
      lng: numberOrNull(state.lng, map.defaultLongitude),
      images,
      mapImage,
    });
  };
  return (
    <MainLayout scrollable title={title}>
      <Stack component="form" spacing={2} sx={{ pb: 2 }} noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfStorage control={control} watch={watch} />
          {error && (
            <Alert severity="error" sx={{ mt: 1, borderRadius: 2 }}>
              {error.message || 'サーバーエラーが発生しました。管理者へお問い合わせください。'}
            </Alert>
          )}
        </Stack>
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Edit,
            label: '保存',
            loading: formState.isSubmitting,
          }}
          secondary={{
            icon: Close,
            end: true,
            label: 'キャンセル',
            onClick: () => navigate({ to: '/settings/storages' }),
          }}
          fullWidth
          spacing={2}
        />
      </Stack>
    </MainLayout>
  );
};
