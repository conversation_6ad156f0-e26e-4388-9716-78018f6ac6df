import { Box, Button, FormHelperText, Grid, Stack } from '@mui/material';

import { useAppContext } from '@/contexts/app-context';

import { queryClient, trpcClient } from '@/api';
import { RhfMapPoint } from '@/components/map/rhf-map-point/_RhfMapPoint';
import { RhfMultiImageSelector } from '@/components/react-hook-form/image-selector/RhfMultiImageSelector';
import { RhfSingleImageSelector } from '@/components/react-hook-form/image-selector/RhfSingleImageSelector';
import {
  fetchAddress,
  fetchPosition,
  geocodeAsync,
  geocoderResultToAddress,
  geocoderResultToPosition,
} from '@/utils';
import { ArrowDownward, ArrowUpward, AutoAwesome } from '@mui/icons-material';
import { type Image, ImageSchema, base64ToBlob, base64ToContentType, isLocalImage } from 'common';
import zip from 'just-zip-it';
import type { MapSettings, Storage } from 'lambda-api';
import { toAddress } from 'models';
import { RhfTextField } from 'mui-ex';
import { enqueueSnackbar } from 'notistack';
import { type Control, type UseFormWatch, useController } from 'react-hook-form';
import { z } from 'zod';

export const StorageStateSchema = z
  .object({
    id: z.string().optional(),
    name: z.string().min(1, '入力してください'),
    code: z.coerce.number(),
    codes: z.array(z.number()),
    sortOrder: z.coerce.number(),
    sectionName: z.string(),
    managerName: z.string(),
    address: z.string(),
    lat: z.number(),
    lng: z.number(),
    images: z.array(ImageSchema),
    mapImage: ImageSchema.nullish(),
  })
  .superRefine((arg, ctx) => {
    if (arg.codes.includes(arg.code))
      ctx.addIssue({
        message: 'コードが重複しています',
        code: z.ZodIssueCode.custom,
        path: ['code'],
      });
  });
export type StorageState = z.infer<typeof StorageStateSchema>;

export const storageToState = (
  storage: Storage,
  storages: Storage[],
  map: MapSettings,
): StorageState => {
  return {
    id: storage.id,
    name: storage.name,
    code: storage.code,
    codes: storages.filter((l) => l.id !== storage.id).map((l) => l.code),
    sortOrder: storage.sortOrder,
    sectionName: storage.sectionName ?? '',
    managerName: storage.managerName ?? '',
    address: storage.address,
    lat: storage.lat ?? map.defaultLatitude,
    lng: storage.lng ?? map.defaultLongitude,
    images: storage.images,
    mapImage: storage.mapImage,
  };
};

export const uploadStorageImageToS3 = async (storageId: string, images: Image[]) => {
  const localImages = images.filter(isLocalImage);
  const res = await trpcClient.presignedUrls.storage.list.query({
    storageId,
    files: localImages.map(({ filename, base64 }) => ({
      contentType: base64ToContentType(base64),
      filename,
    })),
  });
  const uploaded = await Promise.all(
    zip(localImages, res).map(async ([{ base64, description }, { key, url, fields }]) => {
      const body = new FormData();
      for (const [key, value] of Object.entries(fields)) {
        body.append(key, value);
      }
      body.append('file', base64ToBlob(base64));
      const response = await fetch(url, { method: 'POST', body });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${errorText}`);
      }
      return { key, description, base64 };
    }),
  );
  return images.map((image, sortOrder) => {
    if (isLocalImage(image)) {
      const found = uploaded.find((u) => u.base64 === image.base64);
      if (found === undefined) throw new Error('Upload failed');
      return { key: found.key, description: image.description, sortOrder };
    }
    return { key: image.key, description: image.description, sortOrder };
  });
};

export const uploadMapImageToS3 = async (storageId: string, image: Image | null | undefined) => {
  if (image === null || image === undefined) return undefined;

  if (isLocalImage(image)) {
    const payload = {
      storageId,
      file: {
        filename: image.filename,
        contentType: base64ToContentType(image.base64),
      },
    };
    const { key, url, fields } = await trpcClient.presignedUrls.storage.map.get.query(payload);
    const body = new FormData();
    for (const [key, value] of Object.entries(fields)) body.append(key, value);
    body.append('file', base64ToBlob(image.base64));

    const response = await fetch(url, { method: 'POST', body });
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Upload failed: ${errorText}`);
    }
    return { key, description: image.description };
  }

  return { key: image.key, description: image.description };
};

type CommonProps = { readOnly?: boolean };
type Props<T extends StorageState> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<StorageState>;
  watch: UseFormWatch<StorageState>;
};

const fetchAddressAndPosition = async (address: string) => {
  return queryClient.fetchQuery({
    queryKey: ['position', address],
    queryFn: async () => {
      const [result] = await geocodeAsync({ address, language: 'ja' }, false).catch((e: Error) => {
        if (e.cause === 'ZERO_RESULTS') {
          enqueueSnackbar('Google Mapの検索結果は0件でした', { variant: 'info' });
          return [];
        }
        throw e;
      });
      if (result === undefined) return null;
      const fetchedAddress = geocoderResultToAddress(result);
      const position = geocoderResultToPosition(result);
      return { address: fetchedAddress, position };
    },
  });
};

export const RhfStorage = <T extends StorageState>(props: Props<T>) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const { labels } = useAppContext();

  const addressCtrl = useController({ control, name: 'address' });
  const latCtrl = useController({ control, name: 'lat' });
  const lngCtrl = useController({ control, name: 'lng' });

  const name = watch('name');

  const handleAutoFill = async () => {
    const result = await fetchAddressAndPosition(name);
    if (result === null) return;
    const { address, position } = result;
    addressCtrl.field.onChange(toAddress(address, ''));
    latCtrl.field.onChange(position.lat);
    lngCtrl.field.onChange(position.lng);
  };

  const handlePositionToAddress = async () => {
    const position = { lat: latCtrl.field.value, lng: lngCtrl.field.value };
    const address = await fetchAddress(position);
    addressCtrl.field.onChange(toAddress(address, ''));
  };

  const handleAddressToPosition = async () => {
    const position = await fetchPosition(addressCtrl.field.value);
    latCtrl.field.onChange(position.lat);
    lngCtrl.field.onChange(position.lng);
  };

  return (
    <>
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfTextField
            control={control}
            name="name"
            label={labels.domain.storage}
            readOnly={readOnly}
          />
        </Grid>
        <Grid
          size={{ xs: 12, sm: 6 }}
          sx={{ display: 'flex', justifyContent: 'center', alignItems: 'end' }}
        >
          {!readOnly && (
            <Button
              variant="text"
              color="primary"
              endIcon={<AutoAwesome />}
              onClick={handleAutoFill}
              disabled={name.length < 2}
            >
              {labels.p.autoFillAddressAndLatLng}
            </Button>
          )}
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfTextField
            control={control}
            name="code"
            label={labels.system.code}
            number
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfTextField
            control={control}
            name="sortOrder"
            number
            label={labels.system.sortOrder}
            readOnly={readOnly}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfTextField
            control={control}
            name="sectionName"
            label={labels.org.section}
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <RhfTextField
            control={control}
            name="managerName"
            label={`${labels.org.manager}名または${labels.org.person}名`}
            readOnly={readOnly}
          />
        </Grid>

        <Grid size={{ xs: 12 }}>
          <RhfTextField
            control={control}
            name="address"
            label={labels.location.address}
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12 }} sx={{ display: readOnly ? 'none' : undefined }}>
          <Stack direction="row" justifyContent="center" spacing={2}>
            <Button
              variant="text"
              color="primary"
              endIcon={<ArrowDownward />}
              onClick={handleAddressToPosition}
            >
              {labels.p.convertAddressToLatLng}
            </Button>
            <Button
              variant="text"
              color="primary"
              startIcon={<ArrowUpward />}
              onClick={handlePositionToAddress}
            >
              {labels.p.convertLatLngToAddress}
            </Button>
          </Stack>
        </Grid>
        <Grid size={{ xs: 12 }}>
          <Box sx={{ width: 1, height: 300 }}>
            <RhfMapPoint control={control} watch={watch} readOnly={readOnly} />
          </Box>
        </Grid>
        <Grid size={{ xs: 4 }}>
          <RhfSingleImageSelector
            control={control}
            watch={watch}
            imagesPropName="mapImage"
            readOnly={readOnly}
            label="地図画像を選択"
            defaultImageDescription="地図画像"
          />
        </Grid>
      </Grid>
      <Stack>
        <RhfMultiImageSelector
          control={control}
          watch={watch}
          readOnly={readOnly}
          imagesPropName="images"
          max={5}
          newButtonLabel="写真を追加"
          limitHelperText={
            <FormHelperText sx={{ textAlign: 'center' }}>写真は最大５枚です</FormHelperText>
          }
        />
      </Stack>
    </>
  );
};
